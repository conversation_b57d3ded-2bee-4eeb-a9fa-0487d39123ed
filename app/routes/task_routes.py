from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from database.base import get_db_dependency
import schemas
from schemas.base import DeviceType, NodeStatus, NodeType
from database.crud import approval_management, task_management, simulation_data
from database.crud.approval_management import approval_record, ApprovalStatus
from typing import Optional, Dict, Set
from routes import logger
from database.models import (
    NodeInstance,
    WorkflowInstance,
    NodeDefinition,
    EdgeInstance,
)
from typing import List
from database.crud.node_status_service import EnhancedNodeStatusService

router = APIRouter(
    prefix='/approvals',
    tags=['审批管理'],
    responses={
        404: {'description': '节点未找到'},
        400: {'description': '请求参数错误'},
        500: {'description': '服务器内部错误'},
    },
)


async def get_node_incoming_edges(
    db: AsyncSession, node_id: int
) -> List[EdgeInstance]:
    query = (
        select(EdgeInstance)
        .where(EdgeInstance.to_node_instance_id == node_id)
        .options(
            joinedload(EdgeInstance.from_node_instance).joinedload(
                NodeInstance.definition
            )
        )
    )
    result = await db.execute(query)
    return result.scalars().all()


class NodePathInfo:
    """节点路径信息"""

    def __init__(
        self,
        node: NodeInstance,
        is_subprocess: bool = False,
        parent_node: Optional['NodePathInfo'] = None,
    ):
        self.node = node
        self.is_subprocess = is_subprocess
        self.parent_node = parent_node
        self.definition = node.definition
        self.workflow = node.workflow
        self.level = self._calculate_level()
        self.incoming_edges = []  # 初始化为空列表

    async def load_edges(self, db: AsyncSession):
        """异步加载incoming edges"""
        self.incoming_edges = await get_node_incoming_edges(db, self.node.id)

    def _calculate_level(self) -> int:
        """计算节点层级"""
        if not self.parent_node:
            return 0
        return self.parent_node.level + 1

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'node_id': self.node.id,
            'node_name': self.definition.name,
            'assigned_to': self.node.assigned_to.split(',')[0]
            if self.node.assigned_to
            else None,
            'workflow_id': self.workflow.id,
            'workflow_name': self.workflow.definition.name,
            'status': self.node.status.value,
            'level': self.level,
            'is_subprocess': self.is_subprocess,
            'parent_node_id': self.parent_node.node.id
            if self.parent_node
            else None,
            'device_type': self.workflow.device_type.value,  # 添加设备类型
            'machine_type': self.workflow.machine_type,  # 添加机器类型
        }


async def build_node_relations(
    db: AsyncSession, nodes: List[NodePathInfo]
) -> Dict[int, List[int]]:
    """构建节点关系映射"""
    relations = {}
    for node in nodes:
        edges = await get_node_incoming_edges(db, node.node.id)
        relations[node.node.id] = [
            edge.from_node_instance.id for edge in edges
        ]
    return relations


async def get_parent_workflow_nodes(
    db: AsyncSession, node_instance: NodeInstance, visited_nodes: Set[int]
) -> List[NodePathInfo]:
    """递归获取父工作流中的节点，包括所有层级的父节点和它们的上游节点"""
    parent_nodes = []

    async def find_upstream_nodes(node: NodeInstance) -> List[NodePathInfo]:
        """查找节点的所有未完成上游节点"""
        upstream_nodes = []
        edges = await get_node_incoming_edges(db, node.id)
        for edge in edges:
            upstream = edge.from_node_instance
            if (
                upstream.id not in visited_nodes
                and upstream.status.value != NodeStatus.completed.value
            ):
                visited_nodes.add(upstream.id)
                # 递归查找上游节点的上游节点
                upper_nodes = await find_upstream_nodes(upstream)
                upstream_nodes.extend(upper_nodes)
                # 添加当前上游节点
                upstream_nodes.append(
                    NodePathInfo(upstream, False, NodePathInfo(node))
                )
        return upstream_nodes

    async def find_parent_node() -> Optional[NodeInstance]:
        """查找父工作流中的节点"""
        if node_instance.workflow.definition.is_subprocess:
            parent_query = (
                select(NodeInstance)
                .join(NodeInstance.definition)
                .options(
                    joinedload(NodeInstance.definition),
                    joinedload(NodeInstance.workflow).joinedload(
                        WorkflowInstance.definition
                    ),
                )
                .filter(
                    NodeDefinition.subprocess_id
                    == node_instance.workflow.definition.id,
                    NodeInstance.subprocess_instance_id
                    == node_instance.workflow.id,
                )
            )

            result = await db.execute(parent_query)
            return result.scalar_one_or_none()
        return None

    # 1. 首先找到直接父节点
    parent_node = await find_parent_node()

    if parent_node and parent_node.id not in visited_nodes:
        visited_nodes.add(parent_node.id)

        # 2. 查找父节点的所有未完成上游节点
        upstream_nodes = await find_upstream_nodes(parent_node)
        parent_nodes.extend(upstream_nodes)

        # 3. 如果父节点所在的工作流也是子流程，继续递归向上查找
        upper_parents = await get_parent_workflow_nodes(
            db, parent_node, visited_nodes
        )
        parent_nodes.extend(upper_parents)

        # 4. 添加当前父节点
        parent_nodes.append(NodePathInfo(parent_node, False, None))

    return parent_nodes


async def get_subprocess_nodes(
    db: AsyncSession, node_instance: NodeInstance, visited_nodes: Set[int]
) -> List[NodePathInfo]:
    """递归获取子流程中的节点"""
    subprocess_nodes = []

    # 如果是子流程节点，获取子流程实例中的节点
    if (
        node_instance.definition.type == NodeType.subprocess
        and node_instance.subprocess_instance_id
    ):
        subprocess_query = (
            select(NodeInstance)
            .join(NodeInstance.workflow)
            .join(NodeInstance.definition)
            .options(
                joinedload(NodeInstance.definition),
                joinedload(NodeInstance.workflow).joinedload(
                    WorkflowInstance.definition
                ),
            )
            .filter(
                NodeInstance.workflow_instance_id
                == node_instance.subprocess_instance_id,
                NodeInstance.status != NodeStatus.completed.value,
            )
        )

        result = await db.execute(subprocess_query)
        sub_nodes = result.scalars().all()

        for sub_node in sub_nodes:
            if sub_node.id not in visited_nodes:
                visited_nodes.add(sub_node.id)
                # 递归查找更深层的子流程节点
                inner_nodes = await get_subprocess_nodes(
                    db, sub_node, visited_nodes
                )
                subprocess_nodes.extend(inner_nodes)
                # 添加当前子流程节点
                subprocess_nodes.append(
                    NodePathInfo(sub_node, True, NodePathInfo(node_instance))
                )

    return subprocess_nodes


@router.get(
    '/project/{project_id}/node/{node_name}/execution-path',
    response_model=Dict,
    summary='获取节点执行路径',
    description='查询节点状态和所有未完成的上游节点路径，包括子流程和主流程节点',
)
async def get_node_execution_path(
    project_id: str,
    node_name: str,
    device_type: DeviceType = DeviceType.main_machine,  # 设置为必选且默认为主机
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取节点的执行路径和状态

    Parameters:
        project_id (str): 项目ID
        node_name (str): 节点名称
        device_type (DeviceType): 设备类型，默认为主机(main_machine)

    Returns:
        dict:
            - executable: 是否可执行
            - target_node: 目标节点信息
            - blocking_nodes: 阻塞节点列表（按执行顺序排列）
            - node_relations: 节点间的关系
    """
    try:
        # 1. 查询目标节点，添加设备类型过滤
        query = (
            select(NodeInstance)
            .join(NodeInstance.workflow)
            .join(NodeInstance.definition)
            .options(
                joinedload(NodeInstance.definition),
                joinedload(NodeInstance.workflow).joinedload(
                    WorkflowInstance.definition
                ),
            )
            .filter(
                WorkflowInstance.project_id == project_id,
                NodeDefinition.name == node_name,
                WorkflowInstance.device_type == device_type,
            )
        )

        result = await db.execute(query)
        target_node = result.scalar_one_or_none()

        if not target_node:
            raise HTTPException(
                status_code=404,
                detail=f'Node {node_name} not found in project {project_id}',
            )

        # 2. 判断状态
        executable = target_node.status.value != NodeStatus.pending.value

        # 如果状态不是pending,直接返回结果
        if executable:
            return {
                'executable': True,
                'target_node': NodePathInfo(target_node).to_dict(),
                'blocking_nodes': [],
                'node_relations': {},
            }

        # 3. 如果是pending状态,继续查询节点路径
        visited_nodes = set()
        blocking_nodes = []

        # 4. 获取父工作流节点
        parent_nodes = await get_parent_workflow_nodes(
            db, target_node, visited_nodes
        )

        # 4. 获取当前节点的未完成上游节点
        async def find_upstream_nodes(
            node: NodeInstance, visited_nodes: Set[int]
        ) -> List[NodePathInfo]:
            """查找节点的所有未完成上游节点"""
            upstream_nodes = []
            # 获取incoming edges
            edges = await get_node_incoming_edges(db, node.id)

            for edge in edges:
                upstream = edge.from_node_instance
                if (
                    upstream.id not in visited_nodes
                    and upstream.status.value != NodeStatus.completed.value
                ):
                    visited_nodes.add(upstream.id)
                    # 递归查找上游节点的上游节点
                    upper_nodes = await find_upstream_nodes(
                        upstream, visited_nodes
                    )
                    upstream_nodes.extend(upper_nodes)
                    # 添加当前上游节点
                    upstream_nodes.append(
                        NodePathInfo(upstream, False, NodePathInfo(node))
                    )
            return upstream_nodes

        current_level_nodes = await find_upstream_nodes(
            target_node, visited_nodes
        )
        blocking_nodes.extend(current_level_nodes)

        # 5. 获取目标节点的子流程节点
        subprocess_nodes = await get_subprocess_nodes(
            db, target_node, visited_nodes
        )
        blocking_nodes.extend(subprocess_nodes)

        # 6. 合并所有节点并按层级排序
        all_nodes = parent_nodes + blocking_nodes
        sorted_nodes = sorted(all_nodes, key=lambda x: (x.level, x.node.id))

        # 7. 判断是否可执行
        executable = target_node.status.value != NodeStatus.pending.value

        # 8. 构建节点关系
        node_relations = await build_node_relations(db, sorted_nodes)

        return {
            'executable': executable,
            'target_node': NodePathInfo(target_node).to_dict(),
            'blocking_nodes': [node.to_dict() for node in sorted_nodes],
            'node_relations': node_relations,
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f'Error getting node execution path: {str(e)}',
        )


@router.patch(
    '/nodes/batch-assign',
    response_model=schemas.BatchUpdateResponse,
    summary='批量更新节点处理人',
    description='批量更新多个项目节点的处理人信息',
)
async def batch_update_nodes_assignee(
    assign_list: List[schemas.ProjectNodeAssign],
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    批量更新项目中指定节点名称的所有实例处理人

    Parameters:
        assign_list (List[schemas.ProjectNodeAssign]): 节点分配列表
            - project_id: 项目ID
            - node_name: 节点名称
            - assign_to: 处理人ID

    Returns:
        schemas.BatchUpdateResponse: 更新结果
            - success: 是否成功
            - updated_count: 更新的节点数量
            - updated_nodes: 更新的节点ID列表
            - failed_nodes: 更新失败的节点ID列表

    Raises:
        HTTPException:
            - 404: 未找到匹配的节点
            - 400: 更新参数错误
            - 500: 更新操作失败

    示例请求:
    ```json
    [
        {"project_id": "123", "node_name": "node1", "assign_to": "user_001"},
        {"project_id": "124", "node_name": "node2", "assign_to": "user_002"}
    ]
    ```
    """
    try:
        updated_nodes = []
        failed_nodes = []

        for assign_item in assign_list:
            try:
                # 查询匹配的节点实例
                result = await db.execute(
                    select(NodeInstance)
                    .join(NodeInstance.workflow)
                    .join(NodeInstance.definition)
                    .filter(
                        WorkflowInstance.project_id == assign_item.project_id,
                        NodeDefinition.name == assign_item.node_name,
                    )
                )

                nodes = result.scalars().all()
                if not nodes:
                    # 记录未找到节点的情况
                    logger.warning(
                        f'No nodes found for project {assign_item.project_id} '
                        f'with name {assign_item.node_name}'
                    )
                    continue

                # 更新找到的所有节点实例
                update_data = schemas.NodeInstanceUpdate(
                    assigned_to=assign_item.assign_to,
                    notification_frequency=assign_item.notification_frequency,
                )
                node_ids = [node.id for node in nodes]

                for node_id in node_ids:
                    try:
                        # 更新节点
                        await task_management.node_instance.update_node_instance(
                            db, node_id, update_data
                        )
                        updated_nodes.append(node_id)
                    except Exception as e:
                        import traceback

                        traceback.print_exc()
                        logger.error(
                            f'Failed to update node {node_id}: {str(e)}'
                        )
                        failed_nodes.append(node_id)
                        continue

            except Exception as e:
                import traceback

                traceback.print_exc()
                logger.error(
                    f'Failed to process project {assign_item.project_id} '
                    f'node {assign_item.node_name}: {str(e)}'
                )
                continue

        # 提交事务
        await db.commit()

        # 返回更新结果
        return {
            'success': len(failed_nodes) == 0,
            'updated_count': len(updated_nodes),
            'updated_nodes': updated_nodes,
            'failed_nodes': failed_nodes,
        }

    except Exception as e:
        await db.rollback()
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f'Error updating node instances: {str(e)}'
        )


@router.patch(
    '/project/{project_id}/node/{node_name}/assign',
    response_model=schemas.BatchUpdateResponse,
    summary='批量更新项目中特定节点的处理人',
    description='根据项目ID和节点名称更新所有相关节点实例的处理人',
)
async def update_project_nodes_assignee(
    project_id: str,
    node_name: str,
    assign_data: schemas.NodeAssignUpdate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    批量更新项目中特定节点的处理人信息

    Parameters:
        project_id (str): 项目ID
        node_name (str): 节点名称
        assign_data (schemas.NodeAssignUpdate): 更新的处理人数据
            - assigned_to: 分配的处理人

    Returns:
        schemas.BatchUpdateResponse: 更新结果
            - success: 是否成功
            - updated_count: 更新的节点数量
            - updated_nodes: 更新的节点ID列表

    Raises:
        HTTPException:
            - 404: 未找到匹配的节点
            - 400: 更新参数错误
            - 500: 更新操作失败

    示例请求:
    ```json
    {
        "assigned_to": "user_001"
    }
    ```
    """
    try:
        # 1. 查询匹配的节点实例
        result = await db.execute(
            select(NodeInstance)
            .join(NodeInstance.workflow)
            .join(NodeInstance.definition)
            .filter(
                WorkflowInstance.project_id == project_id,
                NodeDefinition.name == node_name,
            )
        )

        nodes = result.scalars().all()
        if not nodes:
            raise HTTPException(
                status_code=404,
                detail=f'No nodes found for project {project_id} with name {node_name}',
            )

        # 2. 批量更新节点
        updated_nodes = []
        update_data = schemas.NodeInstanceUpdate(
            assigned_to=assign_data.assigned_to
        )

        node_ids = [node.id for node in nodes]

        for node_id in node_ids:
            try:
                # 更新每个节点
                await task_management.node_instance.update_node_instance(
                    db, node_id, update_data
                )
                updated_nodes.append(node_id)
            except Exception as e:
                # 记录单个节点更新失败，但继续处理其他节点
                logger.error(f'Failed to update node {node_id}: {str(e)}')
                continue

        # 3. 提交事务
        await db.commit()

        # 4. 返回更新结果
        return {
            'success': True,
            'updated_count': len(updated_nodes),
            'updated_nodes': updated_nodes,
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f'Error updating node instances: {str(e)}'
        )


@router.patch(
    '/{node_id}',
    response_model=schemas.NodeInstance,
    summary='更新节点信息',
    description='更新节点的分配人',
)
async def update_node(
    node_id: int,
    update_data: schemas.NodeInstanceUpdate,
    device_type: Optional[DeviceType] = None,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    更新节点实例信息

    Parameters:
        node_id (int): 节点ID
        update_data (schemas.NodeInstanceUpdate): 更新的节点数据
            - status: 节点状态（可选）
            - assigned_to: 分配的处理人（可选）
            - start_time: 开始时间（可选）
            - end_time: 结束时间（可选）
            - actual_duration: 实际持续时间（可选）

    Returns:
        schemas.NodeInstanceResponse: 更新后的节点信息
            - id: 节点ID
            - workflow_instance_id: 工作流实例ID
            - node_definition_id: 节点定义ID
            - status: 当前状态
            - assigned_to: 处理人
            - start_time: 开始时间
            - end_time: 结束时间
            - actual_duration: 实际持续时间
            - created_at: 创建时间
            - updated_at: 更新时间

    Raises:
        HTTPException:
            - 404: 节点未找到
            - 400: 更新参数错误
            - 500: 更新操作失败

    示例请求:
    ```json
    {
        "status": "active",
        "assigned_to": "user_001",
        "start_time": "2024-01-01T10:00:00Z"
    }
    ```
    """
    try:
        # 1. 检查节点是否存在
        existing_node = await task_management.node_instance.get_node_instance(
            db, node_id
        )
        if not existing_node:
            raise HTTPException(
                status_code=404, detail=f'Node with id {node_id} not found'
            )

        # 2. 验证状态转换的合法性
        if update_data.status:
            await validate_status_transition(
                existing_node.status, update_data.status
            )

        # 3. 处理时间相关的逻辑
        if (
            update_data.status == schemas.NodeStatus.active
            and not update_data.start_time
        ):
            update_data.start_time = datetime.now()
        elif (
            update_data.status == schemas.NodeStatus.completed
            and not update_data.end_time
        ):
            update_data.end_time = datetime.now()

        # 4. 如果设置了结束时间，计算实际持续时间
        if update_data.end_time and existing_node.start_time:
            duration = update_data.end_time - existing_node.start_time
            update_data.actual_duration = round(
                duration.total_seconds() / 3600, 2
            )  # 转换为小时

        # 5. 执行更新操作

        await task_management.node_instance.update_node_instance(
            db, node_id, update_data
        )
        updated_node = await task_management.node_instance.get_node_instance(
            db, node_id
        )

        if not updated_node:
            raise HTTPException(
                status_code=500, detail='Failed to update node instance'
            )

        # 6. 如果状态发生变化，更新工作流状态
        if update_data.status:
            approval_record.service.init_node_status_service(db)
            await approval_record.service.node_status_service.update_node_status(
                node_id, update_data.status, update_data
            )

        return updated_node

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f'Error updating node instance: {str(e)}'
        )


async def validate_status_transition(
    current_status: schemas.NodeStatus, new_status: schemas.NodeStatus
) -> None:
    """
    验证节点状态转换的合法性

    Args:
        current_status: 当前状态
        new_status: 目标状态

    Raises:
        ValueError: 当状态转换不合法时
    """
    # 定义合法的状态转换
    valid_transitions = {
        schemas.NodeStatus.pending: {schemas.NodeStatus.active},
        schemas.NodeStatus.active: {
            schemas.NodeStatus.completed,
            schemas.NodeStatus.error,
        },
        schemas.NodeStatus.error: {schemas.NodeStatus.active},
        schemas.NodeStatus.completed: set(),  # 完成状态是终态
        schemas.NodeStatus.overdue: {schemas.NodeStatus.active},
    }

    if new_status not in valid_transitions.get(current_status, set()):
        raise ValueError(
            f'Invalid status transition from {current_status.value} to {new_status.value}'
        )


async def is_simulation_node(db: AsyncSession, node_id: int) -> bool:
    """检查节点是否为仿真节点"""
    query = (
        select(NodeInstance)
        .options(
            selectinload(NodeInstance.definition),  # 预加载 definition 关系
            selectinload(NodeInstance.workflow),  # 预加载 definition 关系
        )
        .where(NodeInstance.id == node_id)
    )
    result = await db.execute(query)
    node = result.scalar_one_or_none()

    if not node:
        return (
            False,
            node.workflow.project_id,
        )

    # 这里根据你的业务逻辑判断是否为仿真节点
    # 可以通过节点名称、节点类型或其他字段判断
    node_name = node.definition.name.lower()
    device_type = node.workflow.device_type.value
    return (
        node_name == '仿真调试' and device_type == DeviceType.main_machine.value,
        node.workflow.project_id,
    )


async def is_simulation_3d_node(
    db: AsyncSession, node_id: int
) -> tuple[bool, str]:
    """检查节点是否为仿真三维搭建节点"""
    query = (
        select(NodeInstance)
        .options(
            selectinload(NodeInstance.definition),  # 预加载 definition 关系
            selectinload(NodeInstance.workflow),  # 预加载 definition 关系
        )
        .where(NodeInstance.id == node_id)
    )
    result = await db.execute(query)
    node = result.scalar_one_or_none()

    if not node:
        return False, ''

    # 通过definition_id判断是否为仿真三维搭建节点 (node_definition_id = 48)
    definition_id = node.definition.id
    device_type = node.workflow.device_type.value
    return (
        definition_id == 48 and device_type == DeviceType.main_machine.value,
        node.workflow.project_id,
    )


@router.post(
    '/node/{node_id}/approve',
    summary='审批节点任务',
    description='处理节点审批，包括同意和拒绝两种操作',
    response_model=schemas.ApprovalRecord,
)
async def approve_node(
    node_id: int,
    approval: schemas.ApprovalRecordCreate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    处理节点审批请求

    Parameters:
        node_id (int): 节点ID
        approval (schemas.ApprovalRecordCreate): 审批信息
            - approval_status: 审批状态（APPROVED/REJECTED）
            - comments: 审批意见
            - approved_by: 审批人ID

    Returns:
        schemas.ApprovalRecord: 审批记录
            - id: 审批记录ID
            - node_instance_id: 节点实例ID
            - approval_status: 审批状态
            - comments: 审批意见
            - approved_by: 审批人
            - created_at: 审批时间

    Raises:
        HTTPException:
            - 400: 审批参数错误
            - 500: 审批处理失败

    状态流转:
        - 同意：节点状态更新为已完成，触发后续节点
        - 拒绝：节点状态更新为错误，可能触发回退

    示例请求:
    ```json
    {
        "approval_status": "APPROVED",
        "comments": "文档完整，同意通过",
        "approved_by": "user_001"
    }
    ```
    """
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 检查是否为仿真节点
            is_simulation, project_id = await is_simulation_node(db, node_id)
            if is_simulation:
                # 设置节点实例ID

                # 检查是否已存在仿真数据
                existing_data = await simulation_data.simulation_data.get_simulation_data_by_node(
                    db, node_id
                )
                if existing_data:
                    # 如果已存在，则更新
                    update_data = schemas.SimulationDataUpdate(
                        simulation_efficiency=approval.simulation_data.simulation_efficiency,
                        mechanical_issues=approval.simulation_data.mechanical_issues,
                        program_issues=approval.simulation_data.program_issues,
                        remarks=approval.simulation_data.remarks,
                    )
                    result = await simulation_data.simulation_data.update_simulation_data(
                        db, existing_data.id, update_data
                    )
                else:
                    data = schemas.SimulationDataCreate(
                        node_instance_id=node_id,
                        project_id=project_id,
                        simulation_efficiency=approval.simulation_data.simulation_efficiency,
                        mechanical_issues=approval.simulation_data.mechanical_issues,
                        program_issues=approval.simulation_data.program_issues,
                        remarks=approval.simulation_data.remarks,
                    )
                    # 如果不存在，则创建
                    result = await simulation_data.simulation_data.create_simulation_data(
                        db, data
                    )

                await db.commit()

            # 检查是否为仿真三维搭建节点
            is_simulation_3d, project_id_3d = await is_simulation_3d_node(
                db, node_id
            )
            if is_simulation_3d and approval.simulation_data:
                # 检查是否有仿真三维搭建相关数据
                if (
                    approval.simulation_data.simulation_3d_responsible
                    or approval.simulation_data.simulation_3d_start_time
                    or approval.simulation_data.simulation_3d_end_time
                ):

                    # 准备仿真三维搭建数据
                    simulation_3d_data = {
                        'simulation_3d_responsible': approval.simulation_data.simulation_3d_responsible,
                        'simulation_3d_start_time': approval.simulation_data.simulation_3d_start_time,
                        'simulation_3d_end_time': approval.simulation_data.simulation_3d_end_time,
                    }

                    # 执行upsert操作
                    await simulation_data.simulation_data.upsert_simulation_3d_data(
                        db, project_id_3d, simulation_3d_data
                    )
                    await db.commit()

            approval.node_instance_id = node_id
            result = await approval_management.approval_record.create_approval_record(
                db, approval, node_id
            )

            # 检查是否为define_id为7的节点且审批通过，如果是则触发项目创建
            if approval.approval_status.value == 'approved':
                try:
                    # 获取节点详情以检查definition.id
                    node_details = await approval_management.approval_record.get_node_with_details(
                        db, node_id
                    )
                    if node_details and node_details.definition.id == 7:
                        # 获取项目ID
                        project_id = node_details.workflow.project_id

                        # 导入状态管理服务并创建项目
                        from database.crud.status_management import CRUDStatus

                        status_service = CRUDStatus(None)  # 传入None作为模型参数
                        success = await status_service.create_factory_project(
                            db, project_id
                        )

                        if success:
                            logger.info(
                                f'节点审批触发项目创建成功: node_id={node_id}, project_id={project_id}'
                            )
                        else:
                            logger.warning(
                                f'节点审批触发项目创建失败: node_id={node_id}, project_id={project_id}'
                            )

                except Exception as e:
                    # 项目创建失败不应该影响审批流程，只记录日志
                    logger.error(
                        f'节点审批触发项目创建时出错: node_id={node_id}, error={str(e)}'
                    )

            return result

        except ValueError as e:
            error_msg = str(e)
            if 'is currently being processed' in error_msg:
                # 节点正在被其他请求处理
                raise HTTPException(
                    status_code=409, detail=f'节点 {node_id} 正在被其他请求处理，请稍后重试'
                )
            elif 'has already been' in error_msg:
                # 节点已经被审批过
                raise HTTPException(status_code=400, detail=error_msg)
            else:
                raise HTTPException(status_code=400, detail=error_msg)

        except Exception as e:
            error_msg = str(e)

            # 检查是否是数据库锁等待超时
            if (
                'Lock wait timeout exceeded' in error_msg
                or 'Deadlock found' in error_msg
            ):
                retry_count += 1
                if retry_count < max_retries:
                    # 等待一段时间后重试
                    import asyncio

                    await asyncio.sleep(0.1 * retry_count)  # 递增等待时间
                    continue
                else:
                    # 超过最大重试次数
                    raise HTTPException(
                        status_code=500,
                        detail=f'数据库繁忙，请稍后重试。节点 {node_id} 审批失败',
                    )
            else:
                # 其他错误，直接抛出
                import traceback

                traceback.print_exc()
                raise HTTPException(
                    status_code=500, detail=f'审批处理失败: {error_msg}'
                )

    # 如果所有重试都失败了
    raise HTTPException(status_code=500, detail=f'审批处理失败，已重试 {max_retries} 次')


@router.post(
    '/node/{node_id}/activate',
    summary='主动激活节点任务',
    description='将节点状态从pending或completed切换为active',
    response_model=schemas.NodeInstance,
)
async def activate_node(
    node_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    主动激活节点

    Parameters:
        node_id (int): 节点ID

    Returns:
        schemas.NodeInstance: 更新后的节点信息
            - id: 节点ID
            - status: 节点状态
            - start_time: 开始时间
            - end_time: 结束时间 (激活后为None)
            - actual_duration: 实际持续时间 (激活后为None)

    Raises:
        HTTPException:
            - 404: 节点不存在
            - 400: 节点状态错误，无法激活
            - 500: 激活处理失败

    状态流转:
        - pending/completed -> active: 节点状态更新为活动，设置开始时间，清除结束时间

    示例请求:
    ```
    POST /api/node/123/activate
    ```
    """
    try:
        # 获取节点信息
        node_status_service = EnhancedNodeStatusService(db)
        node = await node_status_service.get_node_with_definition(node_id)

        if not node:
            raise HTTPException(status_code=404, detail=f'节点 {node_id} 不存在')

        # 检查节点当前状态
        if node.status.value not in [
            NodeStatus.pending.value,
            NodeStatus.completed.value,
        ]:
            raise HTTPException(
                status_code=400,
                detail=f'节点状态为 {node.status.value}，只有 pending 或 completed 状态的节点可以被激活',
            )

        # 准备更新数据
        update_data = {
            'start_time': datetime.now(),
            'end_time': None,
            'actual_duration': None,
        }

        # 更新节点状态
        approval_record.service.init_node_status_service(db)
        await approval_record.service.node_status_service.update_node_status(
            node_id, NodeStatus.active, update_data=update_data
        )

        # 提交更改
        await db.commit()

        # 获取更新后的节点
        updated_node = await node_status_service.get_node_with_definition(
            node_id
        )

        # 构建返回数据
        return updated_node

    except HTTPException as e:
        # 重新抛出HTTP异常
        raise e
    except ValueError as e:
        # 处理业务逻辑错误
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # 记录并处理未预期的异常
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f'激活节点失败: {str(e)}')


@router.get(
    '/node/{node_id}/history',
    summary='获取节点审批历史',
    description='获取指定节点的所有审批记录历史',
    response_model=list[schemas.ApprovalRecord],
)
async def get_node_approval_history(
    node_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取节点的审批历史记录

    Parameters:
        node_id (int): 节点ID

    Returns:
        List[schemas.ApprovalRecord]: 审批历史记录列表，按时间倒序排序
            - id: 审批记录ID
            - node_instance_id: 节点实例ID
            - approval_status: 审批状态
            - comments: 审批意见
            - approved_by: 审批人
            - created_at: 审批时间

    示例响应:
    ```json
    [
        {
            "id": 1,
            "node_instance_id": 100,
            "approval_status": "APPROVED",
            "comments": "文档完整，同意通过",
            "approved_by": "user_001",
            "created_at": "2024-01-01T12:00:00Z"
        }
    ]
    ```
    """
    approvals = await approval_management.approval_record.get_node_approvals(
        db, node_id
    )
    return approvals


@router.get(
    '/pending',
    summary='获取待审批任务',
    description='获取指定审批人的待处理审批任务列表',
    response_model=list[schemas.PendingApproval],
)
async def get_pending_approvals(
    approver_id: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取待审批任务列表

    Parameters:
        approver_id (str): 审批人ID

    Returns:
        List[schemas.PendingApproval]: 待审批任务列表
            - node_id: 节点ID
            - node_name: 节点名称
            - workflow_id: 工作流ID
            - workflow_name: 工作流名称
            - project_id: 项目ID
            - submit_time: 提交时间
            - submitter: 提交人

    示例响应:
    ```json
    [
        {
            "node_id": 100,
            "node_name": "需求评审",
            "workflow_id": 1,
            "workflow_name": "产品发布流程",
            "project_id": "proj_001",
            "submit_time": "2024-01-01T12:00:00Z",
            "submitter": "user_002"
        }
    ]
    ```
    """
    pending_tasks = (
        await approval_management.approval_record.get_pending_approvals(
            db, approver_id
        )
    )
    return pending_tasks


@router.get(
    '/statistics/{node_id}',
    summary='获取审批统计信息',
    description='获取指定节点的审批统计数据，包括审批总数、通过率等',
    response_model=dict,
)
async def get_approval_statistics(
    node_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取节点的审批统计信息

    Parameters:
        node_id (int): 节点ID

    Returns:
        dict: 审批统计信息
            - node_id: 节点ID
            - total_approvals: 审批总数
            - approval_rate: 审批通过率（百分比）
            - average_approval_time: 平均审批时长（小时）

    示例响应:
    ```json
    {
        "node_id": 100,
        "total_approvals": 10,
        "approval_rate": 80.0,
        "average_approval_time": 24.5
    }
    ```
    """
    approvals = await approval_management.approval_record.get_node_approvals(
        db, node_id
    )

    total_count = len(approvals)
    if total_count == 0:
        return {
            'node_id': node_id,
            'total_approvals': 0,
            'approval_rate': 0,
            'average_approval_time': 0,
        }

    approved_count = sum(
        1 for a in approvals if a.approval_status == ApprovalStatus.approved
    )

    # 计算平均审批时间（只考虑已完成的审批）
    approval_times = []
    for i in range(len(approvals) - 1):
        time_diff = approvals[i].created_at - approvals[i + 1].created_at
        approval_times.append(time_diff.total_seconds() / 3600)  # 转换为小时

    return {
        'node_id': node_id,
        'total_approvals': total_count,
        'approval_rate': (approved_count / total_count) * 100,
        'average_approval_time': sum(approval_times) / len(approval_times)
        if approval_times
        else 0,
    }


@router.get(
    '/node/{node_id}/details',
    response_model=schemas.NodeDetailResponse,
    summary='获取节点详细信息',
    description='获取节点的完整信息，包括基本信息、审批记录、工作流信息等',
)
async def get_node_details(
    node_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取节点任务的详细信息

    Parameters:
        node_id (int): 节点ID

    Returns:
        schemas.NodeDetailResponse: 节点详细信息
            基本信息:
            - node_id: 节点ID
            - name: 节点名称
            - type: 节点类型
            - status: 当前状态
            - color: 节点颜色

            分配信息:
            - assigned_to: 处理人
            - start_time: 开始时间
            - end_time: 结束时间

            时间信息:
            - expected_duration: 预期完成时长（小时）
            - actual_duration: 实际完成时长（小时）

            审批信息:
            - need_approval: 是否需要审批
            - approval_history: 审批历史记录
            - latest_approval: 最新审批记录

            工作流信息:
            - workflow_info: 工作流相关信息
                - workflow_id: 工作流ID
                - workflow_name: 工作流名称
                - project_id: 项目ID
                - workflow_status: 工作流状态
                - workflow_start_time: 工作流开始时间
                - workflow_end_time: 工作流结束时间

    Raises:
        HTTPException:
            - 404: 节点未找到
            - 500: 获取详情失败

    示例响应:
    ```json
    {
        "node_id": 100,
        "name": "需求评审",
        "type": "APPROVAL",
        "status": "RUNNING",
        "color": "#1890ff",
        "assigned_to": "user_001",
        "start_time": "2024-01-01T10:00:00Z",
        "end_time": null,
        "expected_duration": 24,
        "actual_duration": null,
        "task_url": "http://example.com/task/100",
        "need_approval": true,
        "approval_history": [
            {
                "approved_by": "user_002",
                "status": "APPROVED",
                "comments": "通过",
                "created_at": "2024-01-01T12:00:00Z"
            }
        ],
        "workflow_info": {
            "workflow_id": 1,
            "workflow_name": "产品发布流程",
            "project_id": "proj_001",
            "workflow_status": "RUNNING",
            "workflow_start_time": "2024-01-01T10:00:00Z",
            "workflow_end_time": null
        }
    }
    ```
    """
    try:
        # 获取节点实例及其相关信息
        node = await approval_management.approval_record.get_node_with_details(
            db, node_id
        )
        if not node:
            raise HTTPException(
                status_code=404, detail=f'Node instance {node_id} not found'
            )

        approvals = (
            await approval_management.approval_record.get_node_approvals(
                db, node_id
            )
        )

        actual_duration = None
        if node.start_time and node.end_time:
            actual_duration = round(
                (node.end_time - node.start_time).total_seconds() / 3600, 2
            )

        return {
            'node_id': node.id,
            'name': node.definition.name,
            'type': node.definition.type.value,
            'status': node.status.value,
            'color': node.definition.color,
            'assigned_to': node.assigned_to,
            'start_time': node.start_time,
            'end_time': node.end_time,
            'actual_duration': actual_duration,
            'task_url': node.definition.task_url,
            'need_approval': node.definition.need_approval,
            'approval_history': [
                {
                    'approved_by': approval.approved_by,
                    'status': approval.approval_status,
                    'comments': approval.comments,
                    'created_at': approval.created_at,
                }
                for approval in approvals
            ]
            if approvals
            else [],
            'latest_approval': approvals[0] if approvals else None,
            'workflow_info': {
                'workflow_id': node.workflow.id,
                'workflow_name': node.workflow.definition.name,
                'project_id': node.workflow.project_id,
                'workflow_status': node.workflow.status.value,
                'workflow_start_time': node.workflow.start_time,
                'workflow_end_time': node.workflow.end_time,
            },
            'created_at': node.created_at,
            'updated_at': node.updated_at,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'获取节点详情失败: {str(e)}')


@router.get(
    '/projects/assignment-status',
    response_model=List[schemas.ProjectAssignmentStatus],
    summary='获取所有项目节点责任人分配状态',
    description='检查所有项目的节点是否都已分配责任人，返回分配状态和未分配的节点列表',
)
async def get_projects_assignment_status(
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    检查所有项目中节点的责任人分配情况

    Returns:
        List[schemas.ProjectAssignmentStatus]: 项目分配状态列表
            - project_id: 项目ID
            - fully_assigned: 是否所有节点都已分配
            - unassigned_nodes: 未分配责任人的节点列表
                - node_id: 节点ID
                - node_name: 节点名称
                - workflow_name: 所属工作流名称

    示例响应:
    ```json
    [
        {
            "project_id": "project_001",
            "fully_assigned": false,
            "unassigned_nodes": [
                {
                    "node_id": 1,
                    "node_name": "需求评审",
                    "workflow_name": "产品发布流程"
                }
            ]
        }
    ]
    ```
    """
    try:
        # 1. 获取所有项目的工作流实例
        workflow_query = select(WorkflowInstance).options(
            joinedload(WorkflowInstance.definition),
        )
        result = await db.execute(workflow_query)
        workflows = result.scalars().all()

        # 按项目ID分组工作流
        project_workflows = {}
        for workflow in workflows:
            if workflow.project_id not in project_workflows:
                project_workflows[workflow.project_id] = []
            if workflow.device_type == DeviceType.main_machine.value:
                project_workflows[workflow.project_id].append(workflow)
        # 2. 检查每个项目的节点分配情况
        project_statuses = []
        for project_id, project_wfs in project_workflows.items():
            unassigned_nodes = []

            # 获取项目所有工作流中的节点
            for workflow in project_wfs:
                nodes_query = (
                    select(NodeInstance)
                    .options(
                        joinedload(NodeInstance.definition),
                        joinedload(NodeInstance.workflow).joinedload(
                            WorkflowInstance.definition
                        ),
                    )
                    .filter(NodeInstance.workflow_instance_id == workflow.id)
                )
                result = await db.execute(nodes_query)
                nodes = result.scalars().all()

                # 检查每个节点的责任人分配情况
                for node in nodes:
                    if not node.assigned_to:
                        unassigned_nodes.append(
                            {
                                'node_id': node.id,
                                'node_name': node.definition.name,
                                'workflow_name': workflow.definition.name,
                            }
                        )

            # 添加项目状态到结果列表
            project_statuses.append(
                {
                    'project_id': project_id,
                    'fully_assigned': len(unassigned_nodes) == 0,
                    'unassigned_nodes': unassigned_nodes,
                }
            )

        return project_statuses

    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f'Error checking project assignment status: {str(e)}',
        )


@router.get(
    '/simulation/data/all',
    response_model=List[schemas.SimulationDataWithExtendedInfoResponse],
    summary='获取所有ERP的仿真数据',
    description='获取所有项目的仿真数据列表，包含型号信息和节点信息，支持分页查询',
)
async def get_all_simulation_data(
    skip: int = Query(0, ge=0, description='跳过的记录数'),
    limit: int = Query(1000, ge=1, le=10000, description='返回的记录数'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取所有ERP的仿真数据

    Parameters:
        skip (int): 跳过的记录数，默认为0
        limit (int): 返回的记录数，默认为1000，最大10000

    Returns:
        List[SimulationDataWithExtendedInfoResponse]: 仿真数据列表（包含型号和节点信息）
            - id: 仿真数据ID
            - node_instance_id: 节点实例ID
            - project_id: 项目ID/ERP
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间
            - created_by: 创建人
            - created_at: 创建时间
            - updated_at: 更新时间
            - model: 型号（来自erp_base_info表）
            - node_start_time: 节点开始时间（node_definition_id=48）
            - node_end_time: 节点结束时间（node_definition_id=48）
            - node_assigned_to: 节点分配人（node_definition_id=48）

    示例响应:
    ```json
    [
        {
            "id": 1,
            "node_instance_id": 123,
            "project_id": "PRJ-001",
            "simulation_efficiency": "95%",
            "mechanical_issues": "2",
            "program_issues": "1",
            "remarks": "测试完成",
            "simulation_3d_responsible": "张工",
            "simulation_3d_start_time": "2024-01-15T09:00:00",
            "simulation_3d_end_time": "2024-01-15T17:00:00",
            "created_by": "admin",
            "created_at": "2024-01-15T08:00:00",
            "updated_at": "2024-01-15T08:00:00",
            "model": "LBCEV280C/LBCEV281C",
            "node_start_time": "2024-01-10T08:00:00",
            "node_end_time": "2024-01-12T18:00:00",
            "node_assigned_to": "张工"
        }
    ]
    ```
    """
    try:
        simulation_data_list = await simulation_data.simulation_data.get_all_simulation_data_with_extended_info(
            db, skip=skip, limit=limit
        )

        return [
            schemas.SimulationDataWithExtendedInfoResponse.model_validate(data)
            for data in simulation_data_list
        ]

    except Exception as e:
        logger.error(f'获取所有仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取仿真数据失败: {str(e)}')


@router.get(
    '/simulation/data/{erp}',
    response_model=Optional[schemas.SimulationDataWithExtendedInfoResponse],
    summary='根据ERP获取仿真数据',
    description='根据项目ERP号获取对应的仿真数据，包括仿真调试、仿真三维搭建、型号和节点信息',
)
async def get_simulation_data_by_erp(
    erp: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    根据ERP获取仿真数据

    Parameters:
        erp (str): 项目ERP号

    Returns:
        Optional[schemas.SimulationDataWithExtendedInfoResponse]: 仿真数据，如果不存在则返回null
            - id: 仿真数据ID
            - node_instance_id: 节点实例ID
            - project_id: 项目ID/ERP
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间
            - created_by: 创建人
            - created_at: 创建时间
            - updated_at: 更新时间
            - model: 型号（来自erp_base_info表）
            - node_start_time: 节点开始时间（node_definition_id=48）
            - node_end_time: 节点结束时间（node_definition_id=48）
            - node_assigned_to: 节点分配人（node_definition_id=48）

    示例响应:
    ```json
    {
        "id": 1,
        "node_instance_id": 123,
        "project_id": "PRJ-001",
        "simulation_efficiency": "95%",
        "mechanical_issues": "2",
        "program_issues": "1",
        "remarks": "测试完成",
        "simulation_3d_responsible": "张工",
        "simulation_3d_start_time": "2024-01-15T09:00:00",
        "simulation_3d_end_time": "2024-01-15T17:00:00",
        "created_by": "admin",
        "created_at": "2024-01-15T08:00:00",
        "updated_at": "2024-01-15T08:00:00",
        "model": "LBCEV280C/LBCEV281C",
        "node_start_time": "2024-01-10T08:00:00",
        "node_end_time": "2024-01-12T18:00:00",
        "node_assigned_to": "张工"
    }
    ```
    """
    try:
        result = await simulation_data.simulation_data.get_simulation_data_by_erp_with_extended_info(
            db, erp
        )
        if result:
            return (
                schemas.SimulationDataWithExtendedInfoResponse.model_validate(
                    result
                )
            )
        else:
            return (
                schemas.SimulationDataWithExtendedInfoResponse.model_validate(
                    {
                        'id': None,
                        'node_instance_id': None,
                        'project_id': erp,
                        'simulation_efficiency': None,
                        'mechanical_issues': None,
                        'program_issues': None,
                        'remarks': None,
                        'simulation_3d_responsible': None,
                        'simulation_3d_start_time': None,
                        'simulation_3d_end_time': None,
                        'created_by': None,
                        'created_at': None,
                        'updated_at': None,
                        'model': None,
                        'node_start_time': None,
                        'node_end_time': None,
                        'node_assigned_to': None,
                    }
                )
            )
    except Exception as e:
        logger.error(f'获取ERP {erp} 的仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取仿真数据失败: {str(e)}')


@router.post(
    '/simulation/data/{erp}',
    response_model=schemas.SimulationDataResponse,
    summary='为指定ERP创建仿真数据',
    description='为指定的项目ERP创建仿真数据记录',
)
async def create_simulation_data_for_erp(
    erp: str,
    simulation_data_in: schemas.SimulationDataCreate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    为指定ERP创建仿真数据

    Parameters:
        erp (str): 项目ERP号
        simulation_data_in (SimulationDataCreate): 仿真数据创建模型
            - node_instance_id: 节点实例ID（可选，如果不提供会自动查找）
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间
            - created_by: 创建人

    Returns:
        SimulationDataResponse: 创建的仿真数据

    示例请求:
    ```json
    {
        "simulation_efficiency": "95%",
        "mechanical_issues": "2",
        "program_issues": "1",
        "remarks": "仿真调试完成",
        "simulation_3d_responsible": "张工",
        "simulation_3d_start_time": "2024-01-15T09:00:00",
        "simulation_3d_end_time": "2024-01-15T17:00:00",
        "created_by": "admin"
    }
    ```
    """
    try:
        # 设置project_id为ERP
        simulation_data_in.project_id = erp

        _ = await simulation_data.simulation_data.create_simulation_data_by_erp(
            db, erp, simulation_data_in
        )

        await db.commit()

        result = (
            await simulation_data.simulation_data.get_simulation_data_by_erp(
                db, erp
            )
        )

        return schemas.SimulationDataResponse.model_validate(result)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        await db.rollback()
        logger.error(f'为ERP {erp} 创建仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'创建仿真数据失败: {str(e)}')


@router.put(
    '/simulation/data/{erp}',
    response_model=schemas.SimulationDataResponse,
    summary='更新指定ERP的仿真数据',
    description='更新指定项目ERP的仿真数据记录',
)
async def update_simulation_data_for_erp(
    erp: str,
    update_data: schemas.SimulationDataUpdate,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    更新指定ERP的仿真数据

    Parameters:
        erp (str): 项目ERP号
        update_data (SimulationDataUpdate): 仿真数据更新模型
            - simulation_efficiency: 仿真效率(PPM)
            - mechanical_issues: 机械问题个数
            - program_issues: 程序问题个数
            - remarks: 备注信息
            - simulation_3d_responsible: 仿真搭建负责人
            - simulation_3d_start_time: 搭建开始时间
            - simulation_3d_end_time: 搭建结束时间

    Returns:
        SimulationDataResponse: 更新后的仿真数据

    示例请求:
    ```json
    {
        "simulation_efficiency": "98%",
        "mechanical_issues": "1",
        "program_issues": "0",
        "remarks": "仿真调试优化完成"
    }
    ```
    """
    try:
        _ = await simulation_data.simulation_data.update_simulation_data_by_erp(
            db, erp, update_data
        )

        await db.commit()

        result = (
            await simulation_data.simulation_data.get_simulation_data_by_erp(
                db, erp
            )
        )

        return schemas.SimulationDataResponse.model_validate(result)

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        await db.rollback()
        logger.error(f'更新ERP {erp} 的仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'更新仿真数据失败: {str(e)}')


@router.get(
    '/node/{node_instance_id}/approval-info',
    response_model=schemas.NodeApprovalInfoResponse,
    summary='获取节点审批信息',
    description='根据节点实例ID获取节点的开始时间、结束时间和最新审批人信息',
)
async def get_node_approval_info(
    node_instance_id: int,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取节点审批信息

    根据节点实例ID查询：
    1. workflow_node_instances表中的start_time和end_time
    2. workflow_approval_records表中created_at最新的一条记录的approved_by

    Parameters:
        node_instance_id (int): 节点实例ID

    Returns:
        NodeApprovalInfoResponse: 节点审批信息
            - node_instance_id: 节点实例ID
            - start_time: 节点开始时间
            - end_time: 节点结束时间
            - approved_by: 最新审批人（如果没有审批记录则为null）
            - approval_created_at: 最新审批时间（如果没有审批记录则为null）

    Example:
    ```json
    {
        "node_instance_id": 123,
        "start_time": "2024-01-15T08:00:00",
        "end_time": "2024-01-15T18:00:00",
        "approved_by": "admin",
        "approval_created_at": "2024-01-15T17:30:00"
    }
    ```
    """
    try:
        result = await simulation_data.simulation_data.get_node_approval_info(
            db, node_instance_id
        )

        if not result:
            raise HTTPException(
                status_code=404, detail=f'未找到节点实例ID {node_instance_id} 的信息'
            )

        return schemas.NodeApprovalInfoResponse.model_validate(result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'获取节点 {node_instance_id} 审批信息失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取节点审批信息失败: {str(e)}')
