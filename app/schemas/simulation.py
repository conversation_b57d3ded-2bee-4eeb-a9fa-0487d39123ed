from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from .approval import SimulationDataBase


class SimulationDataCreate(SimulationDataBase):
    """创建仿真数据模型"""

    node_instance_id: Optional[int] = Field(
        None, description='节点实例ID（可选，如果不提供会自动查找）'
    )
    created_by: Optional[str] = Field(None, description='创建人')
    project_id: str = Field(..., description='项目ID')


class SimulationDataUpdate(BaseModel):
    """更新仿真数据模型"""

    simulation_efficiency: Optional[str] = Field(None, description='仿真效率(PPM)')
    mechanical_issues: Optional[str] = Field(None, description='机械问题个数')
    program_issues: Optional[str] = Field(None, description='程序问题个数')
    remarks: Optional[str] = Field(None, description='备注信息')
    # 新增仿真三维搭建相关字段
    simulation_3d_responsible: Optional[str] = Field(
        None, description='仿真搭建负责人'
    )
    simulation_3d_start_time: Optional[datetime] = Field(
        None, description='搭建开始时间'
    )
    simulation_3d_end_time: Optional[datetime] = Field(
        None, description='搭建结束时间'
    )


class SimulationDataResponse(SimulationDataBase):
    """仿真数据响应模型"""

    id: Optional[int]
    node_instance_id: Optional[int]
    project_id: Optional[str]
    created_by: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class SimulationDataWithModelResponse(SimulationDataBase):
    """仿真数据响应模型（包含型号信息）"""

    id: Optional[int]
    node_instance_id: Optional[int]
    project_id: Optional[str]
    created_by: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    model: Optional[str] = Field(None, description='型号（来自erp_base_info表）')

    class Config:
        from_attributes = True


class SimulationDataWithExtendedInfoResponse(SimulationDataBase):
    """仿真数据响应模型（包含型号和节点信息）"""

    id: Optional[int]
    node_instance_id: Optional[int]
    project_id: Optional[str]
    created_by: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    model: Optional[str] = Field(None, description='型号（来自erp_base_info表）')
    # 新增的节点信息字段
    node_start_time: Optional[datetime] = Field(
        None, description='节点开始时间（node_definition_id=48）'
    )
    node_end_time: Optional[datetime] = Field(
        None, description='节点结束时间（node_definition_id=48）'
    )
    node_assigned_to: Optional[str] = Field(
        None, description='节点分配人（node_definition_id=48）'
    )

    class Config:
        from_attributes = True


class NodeApprovalInfoResponse(BaseModel):
    """节点审批信息响应模型"""

    node_instance_id: int = Field(..., description='节点实例ID')
    start_time: Optional[datetime] = Field(None, description='节点开始时间')
    end_time: Optional[datetime] = Field(None, description='节点结束时间')
    approved_by: Optional[str] = Field(None, description='最新审批人')
    approval_created_at: Optional[datetime] = Field(None, description='最新审批时间')

    class Config:
        from_attributes = True


class Simulation3DSubmitRequest(BaseModel):
    """仿真三维搭建提交请求模型"""

    simulation_3d_responsible: str = Field(..., description='仿真搭建负责人')
    simulation_3d_start_time: datetime = Field(..., description='搭建开始时间')
    simulation_3d_end_time: datetime = Field(..., description='搭建结束时间')


class SimulationApprovalRequest(BaseModel):
    """仿真节点审批请求模型"""

    # 审批信息
    approval_status: str = Field(..., description='审批状态（APPROVED/REJECTED）')
    comments: Optional[str] = Field(None, description='审批意见')
    approved_by: str = Field(..., description='审批人ID')

    # 仿真数据（可选）
    simulation_data: Optional[SimulationDataBase] = Field(
        None, description='仿真数据'
    )
